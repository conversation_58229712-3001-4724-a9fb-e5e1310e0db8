# 自动主题切换功能说明

## 功能概述

自动主题切换功能可以根据时间自动在浅色模式和深色模式之间切换，提供更好的用户体验。

## 功能特点

### 🕐 基于时间的自动切换
- **浅色模式时间**: 早上6点 - 晚上6点
- **深色模式时间**: 晚上6点 - 早上6点
- **检查频率**: 每5分钟检查一次

### 👤 智能手动切换
- 手动切换后3小时内不会自动切换
- 3小时后自动恢复时间切换
- 保持用户的选择偏好

### 🎨 视觉反馈
- 切换时显示通知提示
- 状态指示器显示当前模式
- 平滑的过渡动画效果

## 配置选项

在 `_config.solitude.yml` 文件中的配置：

```yaml
display_mode:
  type: auto
  universe: false
  # 根据时间自动切换主题
  auto_time:
    enable: true
    # 浅色模式开始时间（小时，24小时制）
    light_start: 6
    # 深色模式开始时间（小时，24小时制）
    dark_start: 18
```

## 使用方法

### 启用功能
1. 确保 `display_mode.auto_time.enable` 设置为 `true`
2. 根据需要调整 `light_start` 和 `dark_start` 时间
3. 重新生成并部署博客

### 手动切换
- 点击右侧导航栏的主题切换按钮
- 手动切换后3小时内保持手动设置的主题
- 3小时后自动恢复基于时间的切换

### 状态查看
- 页面左下角有状态指示器
- 点击状态指示器可查看详细信息
- 不同颜色表示不同状态：
  - 🔵 蓝色/黄色渐变：自动模式
  - 🔴 红色：手动模式

## 技术实现

### 文件结构
```
source/
├── js/
│   └── auto-theme-switcher.js    # 主要逻辑
├── css/
│   └── auto-theme-switcher.css   # 样式文件
└── docs/
    └── auto-theme-switcher.md    # 说明文档
```

### 核心功能
1. **AutoThemeSwitcher 类**: 主要的功能实现
2. **时间检测**: 每5分钟检查当前时间并决定主题
3. **本地存储**: 保存用户的手动切换偏好
4. **事件系统**: 主题切换时触发自定义事件

### API 接口
```javascript
// 获取自动主题切换器实例
const switcher = window.autoThemeSwitcher;

// 手动切换主题
switcher.toggleTheme();

// 获取当前状态
const status = switcher.getStatus();

// 更新配置
switcher.updateConfig({
  lightStartHour: 7,
  darkStartHour: 19
});
```

## 兼容性

- ✅ 支持所有现代浏览器
- ✅ 移动端友好
- ✅ 与现有主题系统兼容
- ✅ 支持 PJAX 页面切换

## 故障排除

### 主题不自动切换
1. 检查配置是否正确启用
2. 确认浏览器支持 localStorage
3. 检查是否在手动切换的3小时内

### 切换时间不准确
1. 检查系统时间是否正确
2. 确认时区设置
3. 刷新页面重新初始化

### 样式异常
1. 确认 CSS 文件正确加载
2. 检查是否有样式冲突
3. 清除浏览器缓存

## 自定义开发

### 修改切换时间
```javascript
// 在页面加载后修改配置
window.autoThemeSwitcher.updateConfig({
  lightStartHour: 7,  // 早上7点开始浅色模式
  darkStartHour: 20   // 晚上8点开始深色模式
});
```

### 监听主题切换事件
```javascript
document.addEventListener('themeChanged', (event) => {
  const { theme, isAutoSwitch } = event.detail;
  console.log(`主题切换到: ${theme}, 是否自动: ${isAutoSwitch}`);
});
```

### 自定义通知样式
修改 `auto-theme-switcher.css` 中的 `.theme-switch-notification` 样式。

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🕐 基于时间的自动切换
- 👤 智能手动切换保护
- 🎨 视觉反馈和状态指示器
- 📱 移动端适配

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个功能！

## 许可证

本功能遵循与 Hexo Theme Solitude 相同的许可证。
